import { UserProfile, ThemeTog<PERSON><PERSON>utton, LanguageSelector } from "@relativity/shared";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

export function Header() {
  const { t } = useLanguage();
  const { logout } = useAuth();
  const { toast } = useToast();

  const handleLogout = () => {
    logout();
    toast({
      title: "Logged Out",
      description: "You have been logged out successfully.",
    });
  };

  return (
    <header className="w-full bg-background border-b border-border px-6 py-3">
      <div className="flex items-center justify-between">
        <h1 className="font-semibold text-foreground text-3xl">
          {t("home.title")}
        </h1>

        <div className="flex items-center space-x-4">
          <LanguageSelector variant="select" />
          <ThemeToggleButton />
          <UserProfile onLogout={handleLogout} />
        </div>
      </div>
    </header>
  );
}
