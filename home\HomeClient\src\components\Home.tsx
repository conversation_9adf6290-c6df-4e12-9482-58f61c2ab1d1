
import { useState } from "react";
import { Header } from "@/components/Header";
import { MainContent } from "@/components/MainContent";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useDecodedJwt } from "@/hooks/useDecodedJwt";


export function Home() {

  const jwtPayload = useDecodedJwt();

  const [searchTerm, setSearchTerm] = useState("");
  const [view, setView] = useState<"grid" | "list">("grid");

  return (
    <div className="min-h-screen container">
      <Header />
      <main className="mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-2xl text-gray-800 dark:text-gray-100 mb-2 font-medium">
            Welcome, {jwtPayload?.firstName || ""}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 font-normal">
            Choose an app to get started.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4 items-center">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search apps..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 focus:border-blue-500 transition-colors text-gray-900 dark:text-gray-100"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={view === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setView("grid")}
              className={`rounded-full px-5 py-2 font-medium transition-colors duration-150 ${view === "grid" ? "bg-blue-600 text-white shadow-md hover:bg-blue-700" : "bg-white dark:bg-gray-900 text-blue-600 border border-blue-600 hover:bg-blue-50 dark:hover:bg-gray-800"}`}
            >
              Grid
            </Button>
            <Button
              variant={view === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setView("list")}
              className={`rounded-full px-5 py-2 font-medium transition-colors duration-150 ${view === "list" ? "bg-blue-600 text-white shadow-md hover:bg-blue-700" : "bg-white dark:bg-gray-900 text-blue-600 border border-blue-600 hover:bg-blue-50 dark:hover:bg-gray-800"}`}
            >
              List
            </Button>
          </div>
        </div>

        {/* App Grid (replacing MainContent for now, but keeping logic) */}
        {/* You can replace MainContent with your AppGrid if needed */}
        <MainContent searchTerm={searchTerm} view={view} />
      </main>
    </div>
  );
}
