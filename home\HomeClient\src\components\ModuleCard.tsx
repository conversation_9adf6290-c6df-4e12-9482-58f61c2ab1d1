
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MoreHorizon<PERSON>, Star } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";


interface Module {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
  category: string;
  isNew?: boolean;
  lastUsed?: Date;
  access?: boolean;
  redirectUrl?: string;
}

interface ModuleCardProps {
  module: Module;
  listView?: boolean;
}

export function ModuleCard({ module, listView = false }: ModuleCardProps) {
  const handleModuleClick = () => {
    const refreshToken = localStorage.getItem("refresh_token");
    let url = module.redirectUrl;
    if (import.meta.env.VITE_LOCALHOST === "true") {
      if (module.id === "relativityadmin") url = "http://localhost:5174";
      if (module.id === "organizationadmin") url = "http://localhost:5179";
    }
    if (!url) {
      if (module.id === "relativityadmin") url = "http://localhost:5174";
      else if (module.id === "organizationadmin") url = "http://localhost:5179";
      else url = "http://localhost:5179";
    }
    window.open(`${url}?refreshToken=${refreshToken}`, "_blank");
  };

  if (listView) {
    return (
      <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <div className={`w-10 h-10 ${module.color} rounded-lg flex items-center justify-center text-white text-lg`}>
            {module.icon}
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="font-bold text-gray-800 dark:text-gray-100">{module.name}</h3>
              {module.isNew && <Badge variant="secondary" className="bg-green-100 text-green-700">New</Badge>}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">{module.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleModuleClick}
            className="bg-blue-600 hover:bg-blue-700 text-white dark:text-white"
          >
            Open
          </Button>
          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white" align="end">
              <DropdownMenuItem>Pin to favorites</DropdownMenuItem>
              <DropdownMenuItem>Add to group</DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">Remove</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu> */}
        </div>
      </div>
    );
  }

  return (
    <div className="group relative">
      <Button
        onClick={handleModuleClick}
        variant="ghost"
        className="w-full h-auto p-4 flex flex-col items-center space-y-3 bg-white dark:bg-gray-900 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-gray-500 hover:scale-105"
      >
        <div className={`w-12 h-12 ${module.color} rounded-lg flex items-center justify-center text-white text-xl shadow-sm`}>
          {module.icon}
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <h3 className="text-sm font-bold text-gray-800 dark:text-gray-100 truncate">{module.name}</h3>
            {module.isNew && (
              <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs px-1 py-0">
                New
              </Badge>
            )}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2 text-wrap">{module.description}</p>
        </div>
      </Button>
      {/* Hover options */}
      {/* <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0 bg-white shadow-sm">
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="bg-white" align="end">
            <DropdownMenuItem>
              <Star className="h-4 w-4 mr-2" />
              Pin to favorites
            </DropdownMenuItem>
            <DropdownMenuItem>Add to group</DropdownMenuItem>
            <DropdownMenuItem className="text-red-600">Remove</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div> */}
      {module.lastUsed && (
        <div className="absolute bottom-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Badge variant="outline" className="text-xs bg-white">
            Recent
          </Badge>
        </div>
      )}
    </div>
  );
}
